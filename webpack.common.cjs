const path = require('path');

module.exports = {
  entry: {
    background: './src/background/index.ts',
    content: './src/content/index.ts',
    popup: './src/main.tsx',
    options: './src/options/main.tsx'
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: {
          loader: 'ts-loader',
          options: {
            compilerOptions: {
              jsx: 'react-jsx',
              esModuleInterop: true,
              allowSyntheticDefaultImports: true,
              allowImportingTsExtensions: true,
              noEmit: true
            }
          }
        },
        exclude: /node_modules/,
      },
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.wasm$/,
        type: 'asset/resource',
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset/resource',
      }
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    }
  },
  output: {
    filename: '[name].js',
    path: path.resolve(__dirname, 'dist/webpack'),
    clean: true,
    publicPath: '/',
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      maxSize: 2000000, // 2MB max chunk size for Chrome extension
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        ai: {
          test: /[\\/]src[\\/]ai[\\/]/,
          name: 'ai-module',
          chunks: 'all',
          priority: 20
        },
        search: {
          test: /[\\/]src[\\/]search[\\/]/,
          name: 'search-module', 
          chunks: 'all',
          priority: 15
        }
      }
    }
  },
  experiments: {
    asyncWebAssembly: true,
    topLevelAwait: true
  },
};